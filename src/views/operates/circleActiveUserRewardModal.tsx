import NewNewsSearchAndInput from '@app/components/common/newNewsSearchAndInput';
import {
    Button,
    Checkbox,
    Col,
    DatePicker,
    Dropdown,
    Form,
    Icon,
    Input,
    InputNumber,
    Menu,
    Modal,
    Radio,
    Row,
    Select,
    Spin,
    Switch,
    Table,
    Tooltip,
    message,
} from 'antd';
import React, { forwardRef, useEffect, useImperativeHandle, useState, useCallback, useMemo } from 'react';

import { communityApi, opApi, searchApi } from '@app/api';
import '@components/business/styles/business.scss';
import { useDispatch } from 'react-redux';
import { setConfig } from '@app/action/config';
import { Drawer, PreviewMCN } from '@app/components/common';
import { PermButton } from '@app/components/permItems';
import moment from 'moment';
import debounce from 'lodash/debounce';

const CircleActiveUserRewardModal = (props: any, ref: any) => {
    const dispatch = useDispatch();

    const [rewardModal, setRewardModal] = useState({
        visible: false,
    });

    const rewardRule : any[] = []
    for (let i = 0; i < 20; i++) {
        if (i < 3) {
            rewardRule[i] = { limit: 500 }
        }
        else if (i < 10) {
            rewardRule[i] = { limit: 300 }
        }
        else {
            rewardRule[i] = { limit: 100 }
        }
        
    }

    const { getFieldDecorator, getFieldValue } = props.form;
    const [dataSource, setDataSource] = useState<any>([]);

    const [preview, setPreview] = useState({
        visible: false,
        skey: Date.now(),
        data: {},
    });

    const [articleModal, setArticleModal] = useState({
        visible: false,
        record: null as any,
        searchResults: [] as any[],
        searching: false,
        selectedMedia: null as any
    });

    useEffect(() => {
        if (props.visible) {
            getList();
        }
        else {
            setDataSource([]);
        }
    }, [props.visible]);

    const getList = () => {
        dispatch(setConfig({ loading: true }));
        communityApi
        .getCircleActiveEarningsList({
            circle_id: props.circle_id,
            month: moment(props.month, 'YYYY-MM').format('YYYYMM'),
            rank_type: 13,
        })
        .then((r: any) => {
            dispatch(setConfig({ loading: false }));
            const data = r.data?.active_circle_user_list;
            if (data && data.length) {
                const targetData = [...data].sort((item1, item2) => item1.rank_index - item2.rank_index)
                targetData.forEach((item) => {
                    item.target_reward = item.reward_money
                    if (!item.is_reward && item.max_reward) {
                        item.reward_money = null
                    }
                })
                setDataSource(targetData);
                setRewardModal({ visible: true })
            }
            else {
                message.error('本月活跃奖励需在次月发放');
                props.onClose();
            }
        })
        .catch(() => {
            dispatch(setConfig({ loading: false }));
            props.onClose();
        });
    };

    const handleReward = () => {
        if (!dataSource || !dataSource.length) {
            message.error('本月活跃奖励需在次月发放')
            return
        }
        const rewards: any[] = []
        for (let i = 0; i < dataSource.length; i++) {
            const item = dataSource[i];
            if ((typeof item.reward_money === 'undefined' || item.reward_money === null) && item.max_reward && item.account_status === 0) {
                message.error('请输入金额')
                return
            }
            else if (!item.article_id && item.max_reward && item.account_status === 0) {
                message.error('稿件不能为空')
                return
            }
            else if (item.max_reward && !item.is_reward && item.account_status === 0) {
                rewards.push({
                    earnings_rank_reward_record_id: item.earnings_rank_reward_record_id,
                    article_id: item.article_id.toString(),
                    rank_index: item.rank_index,
                    reward_money:item.reward_money
                })
            }
        }

        if (rewards.length) {
            dispatch(setConfig({ loading: true }));
            communityApi.giveCircleActiveEarnings({
                rank_month: moment(props.month, 'YYYY-MM').format('YYYYMM'),
                rank_types: [13],
                circle_id: props.circle_id,
                earnings_rank_reward: rewards
            })
            .then((r: any) => {
                if (r.data?.paid_article_ids) {
                    const paidIds = r.data.paid_article_ids;
                    const newDataSource: any = dataSource.map((item: any) => {
                        const targetIds = paidIds.filter((idItem: any) => {
                            return item.article_id === idItem;
                        })
                        if (targetIds) {
                            item.paid_article = true
                        }
                        return item;
                    });
                    setDataSource(newDataSource);

                    message.error('部分稿件无效或已发过稿费');
                }
                else {
                    message.success('奖励发放成功');
                    handleRewardCancel();
                }
            })
            .catch(() => {
                message.error('发放奖励失败');
            })
            .finally(() => {
                dispatch(setConfig({ loading: false }));
            });
        }
        else {
            handleRewardCancel();
        }
    }

    const handleChangeArticle = (record: any) => {
        const selectedMedia = record.article_id ? {
            id: record.article_id,
            list_title: record.article_title || '未知稿件',
            channel_name: '未知频道' // 默认值，实际使用时可能需要从接口获取
            } : null;

            setArticleModal({
            visible: true,
            record: record,
            searchResults: selectedMedia ? [selectedMedia] : [],
            searching: false,
            selectedMedia: selectedMedia
        });
    }

    const handleRewardCancel = () => {
        setRewardModal({ visible: false });
        props.onClose();
    }

    const handleShowArticle = (record: any) => {
        setPreview({ visible: true, skey: Date.now(), data: { ...record, doc_type: 10, id: record.article_id } })
    }

    const onMoneyChange = (value: any, record: any) => {
        record.reward_money = value;
        
        const newDataSource: any = dataSource.map((item: any) => {
            if (item.id === record.id) {
                item.reward_money = value;
            }
            return item;
        });
        setDataSource(newDataSource);
    }

    const handleArticleModalCancel = () => {
        setArticleModal({
            visible: false,
            record: null,
            searchResults: [],
            searching: false,
            selectedMedia: null
        });
    };

    const handleArticleModalOk = () => {
        const { record, selectedMedia } = articleModal;

        if (!record) {
            message.error('未找到对应记录');
            return;
        }

        if (!selectedMedia) {
            message.error('请选择稿件');
            return;
        }

        dispatch(setConfig({ loading: true }));
        communityApi.getEarningsArticleDetail({ article_id: selectedMedia.id })
        .then((r: any) => {
            const data = r.data;
            if (Object.keys(data).length !== 0) {
                message.error('该稿件已发过稿费')
                return;
            }
            else {
                const newDataSource: any[] = dataSource.map((item: any) => {
                    if (item.id === record.id) {
                        item.article_id = selectedMedia.id;
                        item.article_title = selectedMedia.list_title;
                    }
                    return item;
                });
                setDataSource(newDataSource)

                message.success('稿件关联成功');
                handleArticleModalCancel();
            }
        })
        .catch(() => {
            
        })
        .finally(() => {
            dispatch(setConfig({ loading: false }));
        });

        
    };

    const handleArticleSearch = useCallback((value: string) => {
        if (!value) {
            setArticleModal(prev => ({ ...prev, searchResults: [] }));
            return;
        }

        const currentAccountId = articleModal.record?.account_id;
        if (!currentAccountId) {
            message.error('未找到用户账号ID');
            return;
        }

        setArticleModal(prev => ({ ...prev, searching: true }));

        searchApi
            .searchUserArticle({
                account_id: currentAccountId,
                keyword: value,
            })
            .then((res: any) => {
            const list = res.data?.article_list || [];
            setArticleModal(prev => ({ 
                ...prev, 
                searchResults: list,
                searching: false 
            }));
            })
            .catch(() => {
            setArticleModal(prev => ({ 
                ...prev, 
                searchResults: [],
                searching: false 
            }));
            });
    }, [articleModal.record?.account_id]);

    const debouncedSearch = useMemo(() => debounce(handleArticleSearch, 500), [handleArticleSearch]);

    const handleMediaChange = useCallback(
        (value: any) => {
            if (!value) return;

            const selectedItem = articleModal.searchResults.find((item) => item.id === value);
            if (selectedItem) {
            setArticleModal(prev => ({ ...prev, selectedMedia: selectedItem }));
            }
        },
        [articleModal.searchResults]
    );

    const getRewardLimit = (record: any) => {
        // if (record.rank_index > rewardRule.length) {
        //     return 100
        // }
        // else {
        //     return rewardRule[parseInt(record.rank_index) - 1].limit
        // }
        switch (record.reward_level) {
            case '甲等':
                return 500;
            case '乙等':
                return 300;
            case '丙等':
                return 100;
            default:
                break;
        }
    }

    // 表格列
    const columns = [
        {
            title: '排名',
            dataIndex: 'rank_index',
            key: 'rank_index',
            width: 50,
        },
        {
            title: '账号昵称',
            dataIndex: 'nick_name',
            key: 'nick_name',
        },
        {
            title: '金额（元）',
            dataIndex: 'reward_money',
            key: 'reward_money',
            width: 140,
            align: 'center' as const,
            render: (text: any, record: any) => {
                return (
                    <>
                        {(record.max_reward === 1 && record.is_reward === 0 && record.account_status === 0) && <InputNumber
                            onChange={(value) => { onMoneyChange(value, record) }}
                            placeholder={"不能超过" + getRewardLimit(record)}
                            min={0}
                            max={getRewardLimit(record)}
                            // defaultValue={text}
                            style={{ width: '120px' }}
                        />}
                        {(record.is_reward === 1 && record.account_status === 0) && record.reward_money}
                        {(record.max_reward === 0 || record.account_status === 1) && `-`}
                        { (typeof record.reward_money !== 'undefined' && record.reward_money !== null) || !record.max_reward || record.account_status === 1 ? '' : <div style={{ color: '#FF2222', fontSize: '12px' }}>请输入金额</div> }
                    </>
                )
            },
        },
        {
            title: '关联稿件',
            dataIndex: 'article_title',
            key: 'article_title',
            render: (text: any, record: any) => {
                if (record.article_id) return (<a onClick={() => handleShowArticle(record)}>{text}</a>)
                else return record.remark || '-（该账号上个月在本圈没有未发稿费的稿件）'
            },
        },
        {
            title: '操作',
            key: 'action',
            dataIndex: 'action',
            align: 'center' as const,
            width: 100,
            render: (text: any, record: any) => {
                return (<>
                    { (record.max_reward === 1 && record.is_reward === 0 && record.account_status === 0) && <a onClick={() => handleChangeArticle(record)}>修改稿件</a>}
                    {record.article_id || !record.max_reward || record.account_status === 1 ? '' : <div style={{ color: '#FF2222', fontSize: '12px' }}>稿件不能为空</div>}
                    {(record.max_reward === 0 || record.is_reward === 1 || record.account_status === 1) && `-`}
                    {(record.hasOwnProperty('paid_article')) && <div style={{ color: '#FF2222', fontSize: '12px' }}>稿件无效或已发过稿费</div>}
                </>)
            },
        },
    ];

    return (
        <>
            <Modal
                visible={rewardModal.visible}
                width={1000}
                onCancel={handleRewardCancel}
                // footer={<></>}
                maskClosable={false}
                onOk={handleReward}
            >
                <Row>
                    <Col span={4}>
                        <h3>
                            <span style={{ marginRight: '4px' }}>发放上榜奖励</span>
                            <Tooltip
                                title={
                                    <div>
                                        <div style={{ marginBottom: '8px', fontWeight: 'bold' }}>提示：</div>
                                        <div style={{ marginBottom: '6px' }}>
                                            1、系统默认为上榜账号从上个月未发放稿费的作品中，选取浏览量最高的一篇，用于绑定稿费数据，支持手动更改；
                                        </div>
                                        <div style={{ marginBottom: '6px' }}>
                                            2、如果上个月没有未发放过稿费的作品可选，请手动设置一篇未发过稿费的作品；
                                        </div>
                                        <div style={{ marginBottom: '6px' }}>
                                            3、确定发奖后，稿费数据将进入待评级列表，经由领导审批后正式生效。
                                        </div>
                                    </div>
                                }
                                placement="right"
                                >
                                <Icon type="question-circle" />
                            </Tooltip>
                        </h3>
                        
                    </Col>
                    <Col span={2} style={{ lineHeight: '30px' }}>
                    榜单周期：
                    </Col>
                    <Col span={2} style={{ lineHeight: '30px', fontWeight: 'bold' }}>
                        {props.month}
                    </Col>
                </Row>
                <Table bordered dataSource={dataSource} columns={columns} pagination={false} />
            </Modal>
            <PreviewMCN
                { ...preview }
                onClose={() => setPreview({ ...preview, visible: false, data: {} })} 
            />
            <Modal
                title="选择稿费稿件"
                visible={articleModal.visible}
                onOk={handleArticleModalOk}
                onCancel={handleArticleModalCancel}
                width={400}
                okText="确定"
                cancelText="取消"
                destroyOnClose
                > 
                <Select
                    showSearch
                    placeholder="输入ID或标题关联内容"
                    notFoundContent={articleModal.searching ? <Spin size="small" /> : null}
                    filterOption={false}
                    onSearch={debouncedSearch}
                    // onFocus={() => { debouncedSearch('') }}
                    onChange={handleMediaChange}
                    value={articleModal.selectedMedia?.id || undefined}
                    style={{ width: '100%' }}
                    suffixIcon={<Icon type="caret-down" />}
                >
                    {articleModal.searchResults.map((item) => (
                    <Select.Option key={item.id} value={item.id}>
                        {item.id} - {item.list_title}
                    </Select.Option>
                    ))}
                </Select>
            </Modal>
        </>
        
        
    );
};

export default Form.create<any>({ name: 'CircleActiveUserRewardModal' })(
    forwardRef<any, any>(CircleActiveUserRewardModal)
);
