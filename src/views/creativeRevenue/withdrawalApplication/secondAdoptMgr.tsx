import React, { useEffect, useState } from 'react'
import { Button, Col, Dropdown, Form, Icon, Input, Menu, Modal, Radio, Row, Select, Tooltip, Timeline, message } from "antd";
import { requirePerm, getCrumb, setMenuHook, requirePerm4Function, UserDetail } from '@app/utils/utils';
import { useDispatch, useSelector, useStore } from 'react-redux';
import { setConfig } from '@app/action/config';
import { PermA, PermButton } from '@app/components/permItems';
import moment from 'moment';import { debounce } from 'lodash';
import { CommonObject } from '@app/types';
import { getTableList, setTableList } from '@app/action/tableList';
import Table from './components/withdrawTable';
import SortableColumn from '@components/common/sortableColumn';
import { useRouteMatch } from 'react-router-dom';
import { userApi, communityApi, creativeRevenueApi, releaseListApi } from '@app/api';
import UserRewardList from './components/userRewardList';

interface FilterState {
    author_type: number | string;
    search_type: number;
    keyword: string;
    account_id: string;
    payout_status: number | string;
    sort_by?: number;
    sort_asc?: number;
}

export default function SecondAdoptMgr(props: any) {
    const match = useRouteMatch();
    const dispatch = useDispatch();
    const [loading, setLoading] = useState(false);
    const [filter, setFilter] = useState<FilterState>({
        author_type: '',
        search_type: 1,
        payout_status: '',
        keyword: '',
        account_id: '',
    });

    useEffect(() => {
        setMenuHook(dispatch, props);

        const { selectKeys, openKeys } = props;
        dispatch(
            setConfig({ selectKeys, openKeys })
        );
    }, [])

    const exportData = () => {
        Modal.confirm({
            title: '单次最多可导出5000行数据',
            content: '如果当前列表数量超出上限，仅导出前5000行 ',
            okText: '确定',
            cancelText: '取消',
            onOk: () => {
                doExportData();
            },
        });
    }

    const doExportData = () => {
        dispatch(setConfig({ loading: true }));
        creativeRevenueApi
            .withdrawExport({ ...filter, status: 4 })
            .then((res: any) => {
                dispatch(setConfig({ loading: false }));
                const a = document.createElement('a');
                a.href = window.URL.createObjectURL(res.data);
                a.download = `潮新闻-创作收益-提现申请-待审核（二审）-${moment().format('YYYYMMDD')}.xlsx`;
                a.click();
            })
            .catch((err) => {
                dispatch(setConfig({ loading: false }));
            });
    };

    const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
    const [search, setSearch] = useState<{
        keyword: string;
        account_id?: string;
    }>({
        keyword: '',
    });
    const [userDetailModal, setUserDetailModal] = useState({
        visible: false,
        key: Date.now(),
        detail: null,
    });
    const [operateLog, setOperateLog] = useState<{
        visible: boolean;
        logs: any[];
    }>({
        visible: false,
        logs: [],
    });
    const [userRewardList, setUserRewardList] = useState<{
        visible: boolean;
        record: any;
    }>({
        visible: false,
        record: null,
    });
    const [authorSuggestions, setAuthorSuggestions] = useState<any[]>([]);
    const { session } = useStore().getState()
    const tableList = useSelector((state: any) => state.tableList);
    const tableCache = useSelector((state: any) => state.tableCache);
    const { total, current, size, records = [] } = useSelector((state: any) => state.tableList);
    const [isInitialized, setIsInitialized] = useState(false);
    const [withdrawalModalVisible, setWithdrawalModalVisible] = useState(false);
    const [currentWithdrawal, setCurrentWithdrawal] = useState<any>(null);
    const [rejectReason, setRejectReason] = useState<any>(null);
    const [rejectModal, setRejectModal] = useState<{
        visible: boolean;
        reason: string;
        multi?: boolean;
        record?: any;
    }>({
        visible: false,
        reason: '',
        multi: false,
        record: null
    })

    useEffect(() => {
        if (tableCache?.beforeRoute === match?.path && tableCache.records.length > 0) {
            getData({ current: tableCache.current, size: tableCache.size });
        } else {
            getData({ current: 1 });
        }

        setIsInitialized(true);
    }, []);

    const getData = (overlap: CommonObject = {}) => {
        const params = { ...getFilter(), ...overlap };
        dispatch(getTableList('withdrawPendingFirstPassedList', 'list', params));
    };

    const getFilter = () => {
        const { current, size } = tableList;
        const filters: CommonObject = { current, size };

        Object.keys(filter).forEach((key) => {
            const value = filter[key as keyof typeof filter];
            if (value !== '' && value !== undefined) {
                filters[key] = value;
            }
        });

        return filters;
    }

    const handleRejectTextInput = (e: any) => {
        setRejectModal({ ...rejectModal, reason: e.target.value })
    }

    const handleApprove = (record: any) => {
        Modal.confirm({
            title: `通过`,
            content: (<>
                {`确定通过后，系统将自动打款`}
            </>),
            onOk: async () => {
                try {
                    setLoading(true);
                    await creativeRevenueApi.withdrawBatchReview({ ids: record.id, review_status: true });
                    message.success(`通过成功`);
                    getData();
                    if (userRewardList.visible) {
                        handleUserRewardListClose();
                    }
                } catch (error) {
                    console.error('通过失败：', error);
                    message.error('通过失败');
                } finally {
                    setLoading(false);
                }
            },
        });
    }

    const handleReject = (record: any) => {
        setRejectModal({ visible: true, reason: '', multi: false, record })
    }

    const handleBatchApprove = () => {
        if (selectedRowKeys.length === 0) {
            message.error('请选择数据');
            return;
        }

        Modal.confirm({
            title: `批量通过`,
            content: (<>
                {`已选择`}
                <span style={{ color: '#DA011B' }}>{selectedRowKeys.length}</span>
                {`条数据，确定通过后，系统将自动打款`}
            </>),
            onOk: async () => {
                try {
                    setLoading(true);
                    await creativeRevenueApi.withdrawBatchReview({ ids: selectedRowKeys.join(','), review_status: true });
                    message.success(`批量通过成功，共 ${selectedRowKeys.length} 条数据`);
                    setSelectedRowKeys([]);
                    getData();
                } catch (error) {
                    console.error('批量通过失败：', error);
                    message.error('批量通过失败');
                } finally {
                    setLoading(false);
                }
            },
        });
    }

    const handleBatchReject = () => {
        if (selectedRowKeys.length === 0) {
            message.error('请选择数据');
            return;
        }

        setRejectModal({ visible: true, reason: '', multi: true, record: null })

    }

    const handleFilterChange = (key: string, value: any) => {
        if (key === 'search_type') {
            setFilter({
                ...filter,
                [key]: value,
                keyword: '',
            });
            setSearch({
                keyword: '',
                account_id: undefined,
            });
        } else {
            setFilter({
                ...filter,
                [key]: value,
            });
        }
    };

    const handleKey = (e: { which: number }) => {
        if (e.which === 13) {
            handleSearch();
        }
    };

    const handleSearch = () => {
        setFilter({
            ...filter,
            account_id: search.account_id || '',
            keyword: '',
        });
    }

    const handleSelectChange = (selectedKeys: any[]) => {
        setSelectedRowKeys(selectedKeys);
    }

    const showUserRewardList = (record: any) => {
        setUserRewardList({
            visible: true,
            record
        })
    }

    const handleUserRewardListClose = () => {
        setUserRewardList({
            visible: false,
            record: null,
        })
    }

    const getOperateLog = (record: any) => {
        dispatch(setConfig({ loading: true }));
        creativeRevenueApi.withdrawRequestList({
            id: record.id
        })
        .then((r: any) => {
            dispatch(setConfig({ loading: false }));
            const data = r.data?.list;
            if (data) {

                setOperateLog({
                    visible: true,
                    logs: data,
                });
            }
        })
        .catch(() => {
            dispatch(setConfig({ loading: false }));
        });

    }

    const showUserDetailModal = (record: any) => {
        dispatch(setConfig({ loading: true }));
        userApi
            .getUserDetail({ accountId: record.account_id })
            .then((r: any) => {
                setUserDetailModal({
                    visible: true,
                    key: Date.now(),
                    detail: r.data.account,
                });
                dispatch(setConfig({ loading: false }));
            })
            .catch(() => dispatch(setConfig({ loading: false })));
    }

    const showPayoutDetailModal = (record: any) => {
        const detailData = {
            withdraw_method: '支付宝',
            alipay_name: record.cert_name,
            alipay_account: record.payment_account,
        }
        setCurrentWithdrawal(detailData);
        setWithdrawalModalVisible(true);
    }

    const hidePayoutDetailModal = () => {
        setWithdrawalModalVisible(false);
        setCurrentWithdrawal(null);
    };

    const getSeq = (i: number) => (current - 1) * size + i + 1;

    const handleAuthorSearch = debounce((value: string) => {
        if (value) {
            communityApi
                .recommendAccount_Search({ keyword: value })
                .then((res: any) => {
                    setAuthorSuggestions(res.data?.list || []);
                })
                .catch(() => {
                    setAuthorSuggestions([]);
                });
        } else {
            setAuthorSuggestions([]);
        }
    }, 300);

    useEffect(() => {
        if (isInitialized) {
            getData({ current: 1 });
        }
    }, [filter]);

    const columns = [
        {
            title: '序号',
            key: 'seq',
            render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
            width: 60,
        },
        {
            title: '账号昵称',
            dataIndex: 'nick_name',
            key: 'nick_name',
            render: (text: string, record: any) => (
                <div>
                    <PermA perm="" onClick={() => showUserDetailModal(record)}>{text}</PermA>
                    <Icon type="wallet" onClick={() => showPayoutDetailModal(record)} style={{ marginLeft: '4px' }} />
                </div>
            ),
        },
        {
            title: '账号类型',
            dataIndex: 'author_type_name',
            key: 'author_type_name',
            width: 120,
            render(text: string) {
                return text
            }
        },
        {
            title: '提现金额（元）',
            dataIndex: 'apply_amount',
            key: 'apply_amount',
            width: 120,
            render: (text: number, record: any) => {
                if (!text && text !== 0) return '-';
                return (<PermA perm="" onClick={() => { showUserRewardList(record) }}>{ Number.isInteger(text) ? text : text.toFixed(2) }</PermA>);
            },
        },

        {
            title: '申请时间',
            dataIndex: 'apply_time_str',
            key: 'apply_time_str',
            width: 160,
            render: (text: string, record: any) => (
                <PermA perm="" onClick={() => getOperateLog(record)}>{text || '-'}</PermA>
            ),
        },

        {
            title: '操作',
            key: 'action',
            fixed: 'right',
            width: 160,
            render: (text: any, record: any) => {
                return (<div>
                    <PermA perm="earnings_withdraw_request:review" onClick={() => handleApprove(record)}>通过</PermA>
                    <PermA perm="earnings_withdraw_request:review" onClick={() => handleReject(record)} style={{ marginLeft: '10px' }}>不通过</PermA>
                </div>)
            },
        },
    ];

    return (
        <>
        <Row className="layout-infobar">
            <Col span={16}>
                <PermButton perm={"earnings_withdraw_request:request_export_2"} onClick={() => exportData()}>
                    导出数据
                </PermButton>
            </Col>
            <Col span={8} className="layout-breadcrumb">
                {getCrumb(props.breadCrumb)}
            </Col>
        </Row>
        <div className="component-content">
            <Row style={{ marginBottom: 16 }}>
                <Col span={16}>
                    <Form layout="inline">
                        <Form.Item>
                            {requirePerm4Function(
                            session,
                            'earnings_withdraw_request:review'
                            )(
                            <Button
                                onClick={handleBatchApprove}
                                disabled={selectedRowKeys.length === 0}
                                style={{ marginRight: 8 }}
                                loading={loading}
                            >
                                批量通过
                            </Button>
                            )}
                            {requirePerm4Function(
                            session,
                            'earnings_withdraw_request:review'
                            )(
                            <Button
                                onClick={handleBatchReject}
                                disabled={selectedRowKeys.length === 0}
                                style={{ marginRight: 8 }}
                                loading={loading}
                            >
                                批量不通过
                            </Button>
                            )}
                        </Form.Item>
                        <Form.Item>
                            <Select
                                value={filter.author_type}
                                onChange={(value) => handleFilterChange('author_type', value)}
                                style={{ width: 120 }}
                                placeholder="作者类型"
                            >
                            <Select.Option value="">账号类型</Select.Option>
                            <Select.Option value={1}>潮客</Select.Option>
                            <Select.Option value={2}>潮鸣号</Select.Option>
                            </Select>
                        </Form.Item>
                    </Form>
                </Col>
                <Col span={8} style={{ textAlign: 'right' }}>
                    <div style={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center' }}>
                        <Select
                            value={search.account_id}
                            onChange={(value) => {
                                setSearch({ ...search, account_id: value as string });
                            }}
                            onSearch={handleAuthorSearch}
                            placeholder="输入昵称或小潮号"
                            style={{ width: 200, marginRight: 8 }}
                            showSearch
                            allowClear={true}
                            filterOption={false}
                            >
                            {authorSuggestions.map((d: any) => (
                                <Select.Option
                                    style={{
                                        whiteSpace: 'pre-wrap',
                                    }}
                                    key={d.id}
                                    value={d.id}
                                >
                                {`${['潮客 - ', '潮鸣号 - ', '潮鸣号 - '][d.cert_type] || ''}${
                                    d.nick_name
                                } | 小潮号：${d.chao_id}`}
                                </Select.Option>
                            ))}
                        </Select>
                        <Button type="primary" onClick={handleSearch}>
                            <Icon type="search" /> 搜索
                        </Button>
                    </div>
                </Col>
            </Row>

            <Table
                func="withdrawPendingFirstPassedList"
                index="list"
                rowKey="id"
                filter={getFilter()}
                columns={columns}
                pagination={true}
                multi={true}
                selectedRowKeys={selectedRowKeys}
                onSelectChange={handleSelectChange}
                tableProps={{ scroll: { x: 1000 } }}
            />

            <Modal
                visible={userDetailModal.visible}
                key={userDetailModal.key}
                title="用户详情"
                width={800}
                onCancel={() => setUserDetailModal({ ...userDetailModal, visible: false })}
                onOk={() => setUserDetailModal({ ...userDetailModal, visible: false })}
            >
                {userDetailModal.visible && <UserDetail detail={userDetailModal.detail} />}
            </Modal>

            <UserRewardList
                visible={userRewardList.visible}
                record={userRewardList.record}
                onClose={handleUserRewardListClose}
                footer={(<>
                    <PermButton perm="earnings_withdraw_request:review" type="primary" onClick={() => handleApprove(userRewardList.record)}>
                        通过
                    </PermButton>
                    <PermButton perm="earnings_withdraw_request:review" type="default" onClick={() => handleReject(userRewardList.record)} style={{ marginLeft: '8px' }}>
                        不通过
                    </PermButton>
                </>)}
            />

            <Modal
                visible={operateLog.visible}
                title="操作日志"
                cancelText={null}
                onCancel={() => setOperateLog({ ...operateLog, visible: false })}
                onOk={() => setOperateLog({ ...operateLog, visible: false })}
            >
                <div>
                    <Timeline style={{ marginTop: 20 }}>
                    {operateLog.logs?.map((v: any, i: number) => [
                        <Timeline.Item className="timeline-dot-big" data-show={v.date} key={`time${i}`}>
                        &nbsp;
                        </Timeline.Item>,
                        v.actions?.map((action: any, index: number) => (
                        <Timeline.Item
                            className="timeline-dot"
                            data-show={action.time}
                            key={`time${i}-action${index}`}
                        >
                            {action.user}&emsp;{action.action}
                        </Timeline.Item>
                        )),
                    ])}
                    </Timeline>
                </div>
            </Modal>

            <Modal
                title="提现方式"
                visible={withdrawalModalVisible}
                onCancel={hidePayoutDetailModal}
                footer={[
                    <Button key="close" onClick={hidePayoutDetailModal}>
                    关闭
                    </Button>
                ]}
                width={400}
                >
                {currentWithdrawal && (
                    <div>
                        <p>提现方式：{currentWithdrawal.withdraw_method}</p>
                        <p>支付宝姓名：{currentWithdrawal.alipay_name}</p>
                        <p>支付宝账号：{currentWithdrawal.alipay_account}</p>
                    </div>
                )}
            </Modal>

            <Modal
                title={rejectModal.multi ? '批量不通过' : '不通过'}
                visible={rejectModal.visible}
                onCancel={() => { setRejectModal({...rejectModal, visible: false, reason: '', record: null }) }}
                onOk={async () => {
                    if (!rejectModal.reason) {
                        message.error('请填写原因说明');
                        return;
                    }

                    const ids = rejectModal.multi ? selectedRowKeys.join(',') : rejectModal.record.id.toString()

                    try {
                        setLoading(true);
                        await creativeRevenueApi.withdrawBatchReview({ ids: ids, review_status: false, review_notes: rejectModal.reason });
                        message.success(`不通过 成功`);
                        getData();
                        if (rejectModal.multi) {
                            setSelectedRowKeys([]);
                        }
                        if (userRewardList.visible) {
                            handleUserRewardListClose();
                        }
                        setRejectModal({ ...rejectModal, visible: false, reason: '' })
                    } catch (error) {
                        console.error('不通过 失败：', error);
                        message.error('不通过 失败');
                    } finally {
                        setLoading(false);
                    }
                }}
                width={540}
            >
                <>
                    {rejectModal.multi && <div style={{ marginBottom: '8px' }}>
                        {`已选择`}
                        <span style={{ color: '#DA011B' }}>{selectedRowKeys.length}</span>
                        {`条数据`}
                    </div>}
                    <Input.TextArea
                        maxLength={50}
                        rows={4}
                        value={rejectModal.reason}
                        onChange={handleRejectTextInput}
                        placeholder="请填写原因说明，该信息将会通知作者，最多50字"
                    />
                </>
            </Modal>
        </div>
        </>
    );
}