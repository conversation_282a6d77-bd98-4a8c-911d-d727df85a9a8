import React, { useState, useEffect } from 'react';
import { Modal, Form, Radio, Input, message } from 'antd';
import { creativeRevenueApi as api } from '@app/api';
import { FormComponentProps } from 'antd/es/form';

interface BatchRatingModalProps extends FormComponentProps {
  visible: boolean;
  selectedCount: number;
  selectedIds: string[];
  onSuccess: () => void;
  onCancel: () => void;
}

const BatchRatingModal: React.FC<BatchRatingModalProps> = ({
  form,
  visible,
  selectedCount,
  selectedIds,
  onSuccess,
  onCancel,
}) => {
  const { getFieldDecorator, getFieldValue, validateFields, resetFields } = form;
  const [loading, setLoading] = useState<boolean>(false);

  // 当弹窗打开时重置表单
  useEffect(() => {
    if (visible) {
      resetFields();
    }
  }, [visible, resetFields]);

  // 处理确认
  const handleOk = async () => {
    validateFields(async (err, values) => {
      if (err) return;
      
      const { fee_grade, custom_amount } = values;

      try {
        setLoading(true);
        
        // ✅ 批量评级处理
        const params = {
          ids: selectedIds.join(','), // 将选中的ID转换为逗号分隔的字符串
          fee_grade: fee_grade,
          amount: fee_grade === '0' ? custom_amount : '', // 根据接口文档，其他等级传具体金额，标准等级传等级值
        };

        await api.batchUpdateFeeGrade(params);
        message.success(`批量评级成功，共 ${selectedIds.length} 条数据`);
        
        // 关闭弹窗并通知父组件刷新数据
        handleCancel();
        onSuccess();
      } catch (error) {
        console.error('批量评级失败：', error);
      } finally {
        setLoading(false);
      }
    });
  };

  // 处理取消
  const handleCancel = () => {
    resetFields();
    onCancel();
  };

  return (
    <Modal
      title="批量评级"
      visible={visible}
      onOk={handleOk}
      onCancel={handleCancel}
      confirmLoading={loading}
      okText="确定"
      cancelText="取消"
      width={480}
    >
      <Form layout="vertical">
        {/* ✅ 显示已勾选数据数量 */}
        <div style={{ 
          background: '#f6f6f6', 
          padding: '12px', 
          borderRadius: '4px', 
          marginBottom: '16px',
          color: '#666'
        }}>
          已勾选数据：{selectedCount} 条
        </div>
        
        <Form.Item label="稿费等级" required>
          {getFieldDecorator('fee_grade', {
            initialValue: '',
            rules: [
              {
                required: true,
                message: '请选择稿费等级',
              },
            ],
          })(
            <Radio.Group>
              <Radio value="1">甲等 (500元)</Radio>
              <Radio value="2">乙等 (300元)</Radio>
              <Radio value="3">丙等 (100元)</Radio>
              <Radio value="4">丁等 (50元)</Radio>
              <Radio value="5">戊等 (10元)</Radio>
              <Radio value="0">
                其他{' '}
                {getFieldDecorator('custom_amount', {
                  initialValue: undefined,
                  rules: [
                    {
                      validator: (_rule, value, callback) => {
                        // 只有当选择"其他"等级时才验证自定义金额
                        if (getFieldValue('fee_grade') === '0') {
                          if (!value) {
                            message.error('请输入稿费金额');
                            callback('请输入稿费金额');
                            return;
                          }

                          const numValue = Number(value);
                          if (isNaN(numValue) || numValue <= 0) {
                            message.error('请输入有效的稿费金额');
                            callback('请输入有效的稿费金额');
                            return;
                          }

                          if (numValue > 800) {
                            // 自动设置为999
                            form.setFieldsValue({ custom_amount: 800 });
                            callback();
                            return;
                          }
                        }
                        callback();
                      }
                    }
                  ]
                })(
                  <Input
                    placeholder="输入稿费金额"
                    style={{ width: '120px', marginLeft: 8 }}
                    type="number"
                    min={1}
                    max={800}
                    onChange={(e) => {
                      const value = Number(e.target.value);
                      if (value > 800) {
                        message.error('稿费金额不能超过999元');
                        form.setFieldsValue({ custom_amount: 800 });
                      }
                    }}
                  />
                )}
                元
              </Radio>
            </Radio.Group>
          )}
        </Form.Item>
        
        {/* ✅ 批量操作确认提示 */}
        <div style={{ color: '#999', fontSize: '12px', marginTop: '16px' }}>
          注：确认后将对选中的 {selectedCount} 条数据设置等级
        </div>
      </Form>
    </Modal>
  );
};

export default Form.create<BatchRatingModalProps>()(BatchRatingModal);