import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Radio, Table, Button, message, Modal, Form, Select, Spin, Icon, Tooltip } from 'antd';
import { Drawer, PreviewMCN } from '@app/components/common';
import { searchApi, userApi } from '@app/api';
import debounce from 'lodash/debounce';
import { setConfig } from '@app/action/config';
import { useDispatch, useSelector } from 'react-redux';
import moment from 'moment'; // ✅ 导入moment用于时间格式化

interface SendRewardsDrawerProps {
  visible: boolean;
  onClose: () => void;
  onOk?: () => void;
  rankType?: 'cmh' | 'ck'; // ✅ 新增：榜单类型，cmh=潮鸣号，ck=潮客
}

interface RankRecord {
  id: string;
  rank_index: number;
  account_id: string;
  nick_name: string;
  reward_level: string;
  reward_money: number;
  article_id?: string;
  article_title?: string;
  earnings_rank_reward_record_id?: string;
  remark?: string;
  is_reward: boolean;
}

// ✅ Tab配置映射（按接口文档的rank_types参数顺序）
const tabConfig = [
  { key: 'total_top_20_list', label: '总榜TOP20', rankType: 1 },
  { key: 'policy_list', label: '政务', rankType: 2 },
  { key: 'talent_list', label: '达人', rankType: 3 },
  { key: 'abroad_list', label: '海外', rankType: 4 },
  { key: 'media_list', label: '媒体', rankType: 5 },
  { key: 'enterprise_list', label: '企业', rankType: 6 },
  { key: 'education_list', label: '教育', rankType: 7 },
  { key: 'life_list', label: '生活', rankType: 8 },
  { key: 'province_top_20_list', label: '省融媒TOP20', rankType: 9 },
  { key: 'good_article_list', label: '优质稿件榜', rankType: 10 },
];
const chaokeTabConfig = [
  { key: 'chaoke_total_top_list', label: '总榜TOP10', rankType: 11 },
  { key: 'chaoke_new_star_list', label: '新星榜', rankType: 12 },
];
const SendRewardsDrawer: React.FC<SendRewardsDrawerProps> = ({
  visible,
  onClose,
  onOk,
  rankType = 'cmh',
}) => {
  // ✅ 根据榜单类型设置默认选中的tab
  const getDefaultTab = () => {
    return rankType === 'ck' ? 'chaoke_total_top_list' : 'total_top_20_list';
  };

  const [selectedTab, setSelectedTab] = useState(getDefaultTab());
  const [loading, setLoading] = useState(false);
  const [allData, setAllData] = useState<any>(null);
  const [isRewardSent, setIsRewardSent] = useState(false); // ✅ 新增：是否已发放奖励状态
  const [unpaidArticleIds, setUnpaidArticleIds] = useState<string[]>([]); // ✅ 新增：未通过稿件ID数组
  const dispatch = useDispatch();

  // ✅ 稿件预览状态
  const [preview, setPreview] = useState({
    visible: false,
    skey: Date.now(),
    data: {},
  });

  // ✅ 选择稿件弹窗状态
  const [articleModal, setArticleModal] = useState({
    visible: false,
    record: null as RankRecord | null,
    searchResults: [] as any[],
    searching: false,
    selectedMedia: null as any,
  });

  // ✅ 获取所有榜单数据，根据榜单类型调用不同API
  const fetchAllData = () => {
    dispatch(setConfig({ mLoading: true }));

    // ✅ 根据榜单类型选择API和参数
    const apiCall =
      rankType === 'ck'
        ? userApi.getCkEarningsList({ rank_types: '11,12' })
        : userApi.getCmhEarningsList({ rank_types: '1,2,3,4,5,6,7,8,9,10' });

    apiCall
      .then((res: any) => {
        console.log(`get${rankType === 'ck' ? 'Ck' : 'Cmh'}EarningsList`, res);
        if (!res.data.operator) {
          res.data.operator = {
            operator_name: '',
            operator_time: '',
          };
        }

        // ✅ 检查是否已发放奖励：operator存在且operator_name有值
        const hasRewardSent = res.data.operator && res.data.operator.operator_name;
        setIsRewardSent(!!hasRewardSent);

        // ✅ 根据榜单类型设置不同的数据结构
        if (rankType === 'ck') {
          setAllData({
            rank_month: res.data.rank_month,
            operator: res.data.operator || {},
            chaoke_total_top_list: res.data.chaoke_total_top_list || [],
            chaoke_new_star_list: res.data.chaoke_new_star_list || [],
          });
        } else {
          setAllData({
            rank_month: res.data.rank_month,
            operator: res.data.operator || {},
            total_top_20_list: res.data.total_top_20_list || [],
            policy_list: res.data.policy_list || [],
            talent_list: res.data.talent_list || [],
            abroad_list: res.data.abroad_list || [],
            media_list: res.data.media_list || [],
            enterprise_list: res.data.enterprise_list || [],
            education_list: res.data.education_list || [],
            life_list: res.data.life_list || [],
            province_top_20_list: res.data.province_top_20_list || [],
            good_article_list: res.data.good_article_list || [],
          });
        }
        dispatch(setConfig({ mLoading: false }));
      })
      .catch(() => {
        dispatch(setConfig({ mLoading: false }));
      });
  };

  useEffect(() => {
    if (visible && !allData) {
      fetchAllData();
    }

    // ✅ 弹窗关闭时重置数据，确保下次打开重新获取
    if (!visible && allData) {
      setAllData(null);
      setSelectedTab(getDefaultTab()); // ✅ 根据榜单类型重置到对应的第一个tab
      setIsRewardSent(false); // ✅ 重置发放奖励状态
      setUnpaidArticleIds([]); // ✅ 重置未通过稿件ID数组
    }
  }, [visible, rankType]); // ✅ 添加rankType依赖

  // ✅ 当榜单类型改变时，重置选中的tab
  useEffect(() => {
    setSelectedTab(getDefaultTab());
  }, [rankType]);

  // ✅ 根据当前选中的tab获取对应数据
  const getCurrentTabData = (): RankRecord[] => {
    if (!allData) return [];
    const data = allData[selectedTab as keyof Omit<any, 'rank_month' | 'operator'>];
    return Array.isArray(data) ? data : [];
  };

  // ✅ 获取榜单周期显示文本
  const getRankMonth = (): string => {
    if (!allData?.rank_month) return '暂无数据';
    // ✅ 格式化显示 202506 -> 2025-06
    return allData.rank_month;
  };

  // ✅ 修改稿件操作（打开选择稿件弹窗）
  const handleEditContent = (record: RankRecord) => {
    // ✅ 如果已有关联稿件，则预选中该稿件
    const selectedMedia = record.article_id
      ? {
          id: record.article_id,
          list_title: record.article_title || '未知稿件',
          channel_name: '未知频道', // 默认值，实际使用时可能需要从接口获取
        }
      : null;

    setArticleModal({
      visible: true,
      record: record,
      searchResults: selectedMedia ? [selectedMedia] : [],
      searching: false,
      selectedMedia: selectedMedia,
    });
  };

  // ✅ 打开稿件预览
  const handlePreview = (record: RankRecord) => {
    if (!record.article_id) {
      message.warning('该记录没有关联稿件');
      return;
    }

    setPreview({
      visible: true,
      skey: Date.now(),
      data: {
        id: record.article_id,
        article_id: record.article_id,
        list_title: record.article_title,
        doc_type: 10, // 假设是UGC内容，实际应该从接口获取
      },
    });
  };

  // ✅ 关闭稿件预览
  const closePreview = () => {
    setPreview({
      ...preview,
      visible: false,
    });
  };

  // ✅ 关闭选择稿件弹窗
  const handleArticleModalCancel = () => {
    setArticleModal({
      visible: false,
      record: null,
      searchResults: [],
      searching: false,
      selectedMedia: null,
    });
  };

  // ✅ 确认选择稿件
  const handleArticleModalOk = async () => {
    const { record, selectedMedia } = articleModal;

    if (!record) {
      message.error('未找到对应记录');
      return;
    }

    if (!selectedMedia) {
      message.error('请选择稿件');
      return;
    }

    // ✅ 新增验证：遍历当前列表中的所有 article_id，检查是否重复
    if (allData) {
      const listKeys = Object.keys(allData).filter(
        (key) => Array.isArray(allData[key]) && key !== 'rank_month' && key !== 'operator'
      );

      let isDuplicate = false;

      // 遍历所有榜单数据，检查是否存在重复的 article_id
      listKeys.forEach((listKey) => {
        const list = allData[listKey];
        list.forEach((item: any) => {
          // 检查是否存在相同的 article_id（排除当前正在编辑的记录）
          if (item.article_id === selectedMedia.id && item.id !== record.id) {
            isDuplicate = true;
          }
        });
      });

      // 如果发现重复的 article_id，显示提示信息并清空选择
      if (isDuplicate) {
        message.error('该作品已有稿费数据，请重新选择');
        setArticleModal({
          ...articleModal,
          selectedMedia: null, // 清空当前选择的选项
        });
        return;
      }
    }

    // ✅ 原有的 API 验证逻辑
    const res: any = await userApi.checkArticleEarnings({
      article_id: selectedMedia.id,
    });
    if (res.data.is_earnings) {
      message.error('该作品已有稿费数据，请重新选择');
      setArticleModal({
        ...articleModal,
        selectedMedia: null, // 清空当前选择的选项
      });
      return;
    }

    // ✅ 更新表格数据
    if (allData) {
      const currentData = getCurrentTabData();
      const updatedData = currentData.map((item) => {
        if (item.id === record.id) {
          return {
            ...item,
            article_id: selectedMedia.id,
            article_title: selectedMedia.list_title,
            remark: '', // 清空备注
          };
        }
        return item;
      });

      // 更新allData中对应tab的数据
      const newAllData = {
        ...allData,
        [selectedTab]: updatedData,
      };
      setAllData(newAllData);
    }

    // ✅ 检查并移除重复标记：如果当前选择的稿件在 unpaidArticleIds 中，将其移除
    if (unpaidArticleIds.includes(selectedMedia.id)) {
      const updatedUnpaidIds = unpaidArticleIds.filter(id => id !== selectedMedia.id);
      setUnpaidArticleIds(updatedUnpaidIds);
    }

    message.success('稿件关联成功');
    handleArticleModalCancel();
  };

  // ✅ 搜索稿件（参考代码实现方式）
  const handleArticleSearch = useCallback(
    (value: string) => {
      if (!value) {
        setArticleModal((prev) => ({ ...prev, searchResults: [] }));
        return;
      }

      // ✅ 获取当前选中记录的account_id
      const currentAccountId = articleModal.record?.account_id;
      if (!currentAccountId) {
        message.error('未找到用户账号ID');
        return;
      }

      setArticleModal((prev) => ({ ...prev, searching: true }));

      searchApi
        .searchUserArticle({
          account_id: currentAccountId,
          keyword: value,
        })
        .then((res: any) => {
          const list = res.data?.article_list || [];
          setArticleModal((prev) => ({
            ...prev,
            searchResults: list,
            searching: false,
          }));
        })
        .catch(() => {
          setArticleModal((prev) => ({
            ...prev,
            searchResults: [],
            searching: false,
          }));
        });
    },
    [articleModal.record?.account_id]
  );

  // ✅ 防抖搜索（参考代码实现方式）
  const debouncedSearch = useMemo(() => debounce(handleArticleSearch, 500), [handleArticleSearch]);

  // ✅ 选择稿件（参考代码实现方式）
  const handleMediaChange = useCallback(
    (value: any) => {
      if (!value) return;

      const selectedItem = articleModal.searchResults.find((item) => item.id === value);
      if (selectedItem) {
        setArticleModal((prev) => ({ ...prev, selectedMedia: selectedItem }));
      }
    },
    [articleModal.searchResults]
  );

  // ✅ 表格列配置
  const columns = [
    {
      title: '排名',
      dataIndex: 'rank_index',
      key: 'rank_index',
      width: 60,
      align: 'center' as const,
    },
    {
      title: '账号昵称',
      dataIndex: 'nick_name',
      key: 'nick_name',
      width: 120,
    },
    {
      title: '等级',
      dataIndex: 'reward_level',
      key: 'reward_level',
      width: 80,
      align: 'center' as const,
    },
    {
      title: '金额 (元)',
      dataIndex: 'reward_money',
      key: 'reward_money',
      width: 100,
      align: 'center' as const,
    },
    {
      title: '关联稿件',
      key: 'article_info',
      ellipsis: true,
      render: (text: string, record: RankRecord) => {
        // ✅ 如果已发放奖励或没有稿件标题，显示备注
        if (record.article_title && record.article_id) {
          // ✅ 有稿件标题和ID，显示可点击的链接
          return (
            <a onClick={() => handlePreview(record)}title="点击预览稿件">
              {record.article_title}
            </a>
          );
        }
        if (record.is_reward && record.article_id) {
          // ✅ 有稿件标题和ID，显示可点击的链接
          return (
            <a onClick={() => handlePreview(record)} title="点击预览稿件">
              {record.article_title}
            </a>
          );
        }
        if (record.remark) {
          return (
            <span style={{ color: '#999' }}>{record.remark ? `- (${record.remark})` : '-'}</span>
          );
        }

        return <span style={{ color: '#999' }}>-</span>;
      },
    },
    {
      title: '操作',
      key: 'operation',
      width: 200,
      align: 'center' as const,
      render: (_: any, record: any) => {
        // ✅ 检查当前稿件是否在未通过列表中
        const isUnpaidArticle = unpaidArticleIds.includes(record.article_id);
        console.log(unpaidArticleIds.includes(record.article_id))
        // ✅ 检查是否未发放奖励但没有稿件ID
        const isEmptyArticle = !record.is_reward && !record.article_id;

        // ✅ 只要未发放奖励就显示修改稿件按钮（无论是否已有关联稿件）
        if (!record.is_reward && record.max_reward !== 0 && !isRewardSent && record.account_status === 0) {
          return (
            <div>
              <a onClick={() => handleEditContent(record)} style={{ marginRight: 8 }}>
                修改稿件
              </a>
              {/* ✅ 如果稿件在未通过列表中，显示提示信息 */}
              {isUnpaidArticle && (
                <div
                  style={{
                    marginTop: 4,
                    color: '#ff4d4f',
                    fontSize: '12px',
                    textAlign: 'center',
                  }}
                >
                  该稿件重复或已失效
                </div>
              )}
              {/* ✅ 如果未发放奖励但没有稿件ID，显示稿件不能为空提示 */}
              {isEmptyArticle && (
                <div
                  style={{
                    marginTop: 4,
                    color: '#ff4d4f',
                    fontSize: '12px',
                    textAlign: 'center',
                  }}
                >
                  稿件不能为空
                </div>
              )}
            </div>
          );
        }

        return <span style={{ color: '#999' }}>-</span>;
      },
    },
  ];

  // ✅ 确认发放奖励，根据榜单类型调用不同API
  const handleConfirm = () => {
     // ✅ 新增验证：检查是否存在未选择稿件且max_reward不为0的记录
    const listKeys = Object.keys(allData).filter(
      (key) => Array.isArray(allData[key]) && key !== 'rank_month' && key !== 'operator'
    );

    let hasUnselectedArticle = false;

    // 遍历所有榜单数据，检查是否存在未选择稿件的记录
    listKeys.forEach((listKey) => {
      const list = allData[listKey];
      list.forEach((item: any) => {
        // 检查是否存在未选择稿件且max_reward不为0的记录
        if (!item.article_id && item.max_reward !== 0 && item.account_status === 0) {
          hasUnselectedArticle = true;
        }
      });
    });

    // 如果存在未选择稿件的记录，显示警告并阻止操作
    if (hasUnselectedArticle) {
      message.warning('存在未选择稿件奖励，请检查提交数据');
      return;
    }

    // ✅ 新增验证：检查稿件重复问题
    const articleIdCount: { [key: string]: number } = {};
    const duplicateArticleIds: string[] = [];

    // 遍历所有榜单数据，统计每个 article_id 的出现次数
    listKeys.forEach((listKey) => {
      const list = allData[listKey];
      list.forEach((item: any) => {
        if (item.article_id) {
          articleIdCount[item.article_id] = (articleIdCount[item.article_id] || 0) + 1;
        }
      });
    });

    // 找出重复的 article_id
    Object.keys(articleIdCount).forEach((articleId) => {
      if (articleIdCount[articleId] > 1) {
        duplicateArticleIds.push(articleId);
      }
    });

    // 如果存在重复的稿件，显示警告并阻止操作
    if (duplicateArticleIds.length > 0) {
      setUnpaidArticleIds(duplicateArticleIds);
      message.warning('部分稿件重复，请查看列表中的提示信息');
      return;
    }

    dispatch(setConfig({ mLoading: true }));

    // ✅ 根据榜单类型设置不同的提交数据结构
    let submitData: any = {
      rank_month: allData.rank_month,
      uuid_article_id: {},
    };

    // ✅ 根据榜单类型设置rank_types
    if (rankType === 'ck') {
      submitData.rank_types = [11, 12]; // 潮客榜单的rank_types
    } else {
      submitData.rank_types = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]; // 潮鸣号榜单的rank_types
    }

    // 遍历所有榜单数据,收集带有article_id的记录
    let articleIdMap: { [key: string]: string } = {};


    // 遍历每个榜单
    listKeys.forEach((listKey) => {
      const list = allData[listKey];
      list.forEach((item: any) => {
        if (item.article_id) {
          articleIdMap[item.earnings_rank_reward_record_id] = item.article_id;
        }
      });
    });

    // 转换成JSON字符串
    submitData.uuid_article_id = articleIdMap;
    console.log('submitData:', submitData);

    // ✅ 根据榜单类型调用不同的API
    const apiCall =
      rankType === 'ck' ? userApi.ckGiveReward(submitData) : userApi.cmhGiveReward(submitData);

    apiCall
      .then((res: any) => {
        if (res.data && res.data.paid_article_ids) {
          // ✅ 如果返回数据中存在 paid_article_ids，说明有未通过的稿件
          setUnpaidArticleIds(res.data.paid_article_ids);
          message.warning('部分稿件未通过审核，请查看列表中的提示信息');
        } else {
          message.success('奖励发放成功！');
          onClose();
        }
      })
      .catch((error) => {
        console.error('发放奖励失败:', error);
      })
      .finally(() => {
        dispatch(setConfig({ mLoading: false }));
      });
  };

  return (
    <Drawer
      title={`发放上榜奖励 - ${rankType === 'ck' ? '潮客榜单' : '潮鸣号榜单'}`}
      visible={visible}
      skey="SendRewardsDrawer"
      onClose={onClose}
      onOk={isRewardSent ? undefined : handleConfirm} // ✅ 已发放奖励时隐藏确定按钮
      okText="确定"
      closeText={isRewardSent ? '关闭' : '取消'} // ✅ 已发放奖励时改为关闭按钮
      width={1100}
      maskClosable={false}
    >
      <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
        {/* ✅ 筛选项 - 使用Radio.Group */}
        <div
          style={{
            marginBottom: 20,
            padding: '16px 24px',
            background: '#fafafa',
            borderRadius: '4px',
          }}
        >
          <div style={{ marginBottom: 8, fontWeight: 'bold' }}>
            榜单周期：{getRankMonth()}{' '}
            <Tooltip
              title={
                <div>
                  <div style={{ marginBottom: '8px', fontWeight: 'bold' }}>提示：</div>
                  <div style={{ marginBottom: '6px' }}>
                    1、系统默认为上榜账号从上个月未发放稿费的作品中，选取浏览量最高的一篇，用于绑定稿费数据，支持手动更改；
                  </div>
                  <div style={{ marginBottom: '6px' }}>
                    2、如果上个月没有未发放过稿费的作品可选，请手动设置一篇未发过稿费的作品；
                  </div>
                  <div style={{ marginBottom: '6px' }}>
                    3、如果账号在多个榜单上榜，自动按金额最高的一个发放上榜奖励的稿费；
                  </div>
                  <div>4、确定发奖后，稿费数据将进入待评级列表，经由领导审批后正式生效。</div>
                </div>
              }
              placement="right"
            >
              <Icon type="question-circle" />
            </Tooltip>
          </div>
          <Radio.Group
            value={selectedTab}
            onChange={(e) => setSelectedTab(e.target.value)}
            buttonStyle="solid"
          >
            {/* ✅ 根据榜单类型显示不同的tab配置 */}
            {(rankType === 'ck' ? chaokeTabConfig : tabConfig).map((tab) => (
              <Radio.Button key={tab.key} value={tab.key}>
                {tab.label}
              </Radio.Button>
            ))}
          </Radio.Group>
        </div>

        {/* ✅ 表格内容 */}
        <Table
          columns={columns}
          dataSource={getCurrentTabData()}
          rowKey="id"
          loading={loading}
          pagination={false}
          size="middle"
          scroll={{ y: 600 }}
          style={{
            background: '#fff',
          }}
        />

        {/* ✅ 已发放奖励提示文案 */}
        {isRewardSent && allData?.operator && (
          <div
            style={{
              marginTop: 16,
              padding: '12px 16px',
              backgroundColor: '#f6ffed',
              border: '1px solid #b7eb8f',
              borderRadius: '4px',
              color: '#52c41a',
            }}
          >
            已提交奖励数据，操作人：{allData.operator.operator_name}，操作时间：
            {allData.operator.operator_time
              ? moment(allData.operator.operator_time).format('YYYY-MM-DD HH:mm:ss')
              : ''}
          </div>
        )}
      </div>
      <PreviewMCN
        visible={preview.visible}
        skey={preview.skey}
        data={preview.data}
        onClose={closePreview}
      />

      {/* ✅ 选择稿件弹窗 */}
      <Modal
        title="选择稿费稿件"
        visible={articleModal.visible}
        onOk={handleArticleModalOk}
        onCancel={handleArticleModalCancel}
        width={400}
        okText="确定"
        cancelText="取消"
        destroyOnClose
      >
        <Select
          showSearch
          placeholder="输入ID或标题关联内容"
          notFoundContent={articleModal.searching ? <Spin size="small" /> : null}
          filterOption={false}
          onSearch={debouncedSearch}
          onChange={handleMediaChange}
          value={articleModal.selectedMedia?.id || undefined}
          style={{ width: '100%' }}
          suffixIcon={<Icon type="caret-down" />}
        >
          {articleModal.searchResults.map((item) => (
            <Select.Option key={item.id} value={item.id}>
              {item.id} - {item.list_title}
            </Select.Option>
          ))}
        </Select>
      </Modal>
    </Drawer>
  );
};

export default SendRewardsDrawer;
