import { setConfig } from '@action/config';
import { getCrumb, requirePerm4Function } from '@app/utils/utils';
import {
  Button,
  Col,
  Dropdown,
  Icon,
  Input,
  InputNumber,
  Menu,
  Modal,
  Row,
  Select,
  Tooltip,
  message,
} from 'antd';
import { useSelector, useDispatch, useStore } from 'react-redux';
import { Table, OrderColumn } from '@components/common';
import { getTableList } from '@app/action/tableList';
import ReactClipboard from 'react-clipboardjs-copy';
import React, { useEffect, useState } from 'react';
import AddCircleDrawer from './component/AddCircleDrawer';
import { useHistory } from 'react-router';
import { PermButton } from '@app/components/permItems';
import { communityApi as api } from '@app/api';
import moment from 'moment';
import AddSelectedCircleModal from './component/addSelectedCircleModal';

export default function CircleMgr(props: any) {
  const dispatch = useDispatch();
  const history = useHistory();
  const { session } = useStore().getState();
  const { current, size, total, records } = useSelector((state: any) => state.tableList);
  const [filter, setFilter] = useState({
    enabled: '',
    show_style: '',
    circle_class_id: '',
    search_type: 1,
    keyword: '',
  });
  const [searchState, setSearchState] = useState({
    search_type: 1,
    keyword: '',
  });

  const [selectedCircle, setSelectedCircle] = useState({
    visible: false,
    records: null,
    key: Date.now() + 1,
  });

  const [circleDrawer, setCircleDrawer] = useState<any>({
    visible: false,
    record: null,
    key: Date.now(),
  });

  const [circleClassList, setCircleClassList] = useState<any[]>([]);

  const getSeq = (i: number) => (current - 1) * size + i + 1;
  const hasFilter = Boolean(
    filter.enabled || filter.show_style !== '' || filter.circle_class_id !== '' || filter.keyword
  );
  const canSeeContent = session.permissions.indexOf('circle_article:list') > -1;
  const canSeeBlock = session.permissions.indexOf('circle_board:list') > -1;

  const handleEditCircle = (record: any) => {
    setCircleDrawer({
      record,
      visible: true,
      key: Date.now(),
    });
  };

  const toggleStatus = (record: any) => {
    // 下架
    Modal.confirm({
      title: record.enabled
        ? '圈子下线后将无法访问，圈子下的内容也不显示关联该圈子'
        : '圈子上线后将恢复访问，圈子下的内容也将恢复显示关联该圈子',
      onOk: () => {
        dispatch(setConfig({ loading: true }));
        api
          .updateCircleStatus({ id: record.id, enabled: !record.enabled })
          .then(() => {
            message.success('操作成功');
            dispatch(setConfig({ loading: false }));
            getData();
          })
          .catch(() => {
            dispatch(setConfig({ loading: false }));
          });
      },
    });
  };

  const deleteCircle = (record: any) => {
    Modal.confirm({
      title: '确定删除该圈子？',
      content: '圈子下的内容不会被删除，只是与圈子解绑',
      onOk: () => {
        dispatch(setConfig({ loading: true }));
        api
          .deleteCircle({ id: record.id })
          .then(() => {
            message.success('操作成功');
            dispatch(setConfig({ loading: false }));
            getData();
          })
          .catch(() => {
            dispatch(setConfig({ loading: false }));
          });
      },
    });
  };

  const exchangeOrder = (id: number, current: number, offset: number) => {
    dispatch(setConfig({ loading: true }));
    api
      .sortCircle({ id, current, offset })
      .then(() => {
        message.success('操作成功');
        getData();
        dispatch(setConfig({ loading: false }));
      })
      .catch(() => {
        dispatch(setConfig({ loading: false }));
      });
  };

  const changeOrder = (record: any, i: any) => {
    let position = i;
    const WAIT_TIME = 1000;
    const positionChange = (v: any) => {
      position = v;
    };

    Modal.confirm({
      title: <p>排序：《{record.name}》</p>,
      icon: <Icon type="info-circle" />,
      content: (
        <div>
          <span>请输入位置：</span>
          <InputNumber min={1} max={total} defaultValue={position} onChange={positionChange} />
        </div>
      ),
      onOk: (closeFunc: Function) => {
        if (!position) {
          message.error('请填写位置');
          return;
        }

        dispatch(setConfig({ loading: true }));
        api
          .sortCircle({ id: record.id, current: i, offset: position - i })
          .then((res: any) => {
            message.success('操作成功');
            getData();
            dispatch(setConfig({ loading: false }));
            closeFunc();
          })
          .catch(() => {
            dispatch(setConfig({ loading: false }));
          });
      },
    });
  };
  const getDropDown = (record: any, i: number) => {
    const canCopy = session.permissions.indexOf('circle:copy_url') > -1;
    const menu = (
      <Menu>
        {requirePerm4Function(
          session,
          'circle:update'
        )(<Menu.Item onClick={() => handleEditCircle(record)}>编辑圈子信息</Menu.Item>)}
        {requirePerm4Function(
          session,
          'circle_article:list'
        )(
          <Menu.Item
            onClick={() => history.push(`/view/circleContentMgr/${record.id}/${record.name}`)}
          >
            管理内容
          </Menu.Item>
        )}
        {requirePerm4Function(
          session,
          'circle_board:list'
        )(
          <Menu.Item
            onClick={() => history.push(`/view/circleBlockMgr/${record.id}/${record.name}`)}
          >
            管理版块
          </Menu.Item>
        )}
        {requirePerm4Function(
          session,
          'circle_manager:list'
        )(
          <Menu.Item
            onClick={() => history.push(`/view/circleOwnerMgr/${record.id}/${record.name}`)}
          >
            管理主理人
          </Menu.Item>
        )}
        {requirePerm4Function(
          session,
          'gpt_question:2:list'
        )(
          <Menu.Item
            onClick={() => history.push(`/view/circleAIMgr?id=${record.id}&name=${record.name}`)}
          >
            数字主理人
          </Menu.Item>
        )}
        {requirePerm4Function(
          session,
          'circle:active'
        )(
          <Menu.Item
            onClick={() => history.push(`/view/circleActiveUser/${record.id}/${record.name}`)}
          >
            活跃圈友
          </Menu.Item>
        )}
        {requirePerm4Function(
          session,
          'circle:update_sort'
        )(<Menu.Item disabled={!record.enabled} onClick={() => changeOrder(record, getSeq(i))}>排序</Menu.Item>)}
        {requirePerm4Function(
          session,
          'circle:update_status'
        )(
          <Menu.Item onClick={() => toggleStatus(record)}>
            {record.enabled ? '下线' : '上线'}
          </Menu.Item>
        )}
        {requirePerm4Function(
          session,
          'circle:delete'
        )(<Menu.Item onClick={() => deleteCircle(record)}>删除</Menu.Item>)}
        <Menu.Item disabled={!canCopy}>
          {canCopy && record.url ? (
            <ReactClipboard
              action="copy"
              text={record.url}
              onSuccess={() => message.success('链接已复制')}
              onError={() => message.error('复制失败')}
            >
              <a>复制链接</a>
            </ReactClipboard>
          ) : (
            '复制链接'
          )}
        </Menu.Item>
      </Menu>
    );
    return (
      <Dropdown overlay={menu}>
        <a className="ant-dropdown-link">
          操作 <Icon type="down" />
        </a>
      </Dropdown>
    );
  };
  const columns: any = [
    {
      title: '排序',
      key: 'order',
      render: (text: any, record: any, i: number) => {
        const pos = getSeq(i);
        return (
          <OrderColumn
            pos={pos}
            start={1}
            end={total}
            perm="circle:update_sort"
            disableUp={hasFilter || !record.enabled || (i > 0 && !records[i - 1].enabled)}
            disableDown={
              hasFilter || !record.enabled || (i < records.length - 1 && !records[i + 1].enabled)
            }
            onUp={() => exchangeOrder(record.id, getSeq(i), -1)}
            onDown={() => exchangeOrder(record.id, getSeq(i), 1)}
          />
        );
      },
      width: 70,
    },
    {
      title: '序号',
      key: 'seq',
      render: (a: any, b: any, index: number) => <span>{getSeq(index)}</span>,
      width: 90,
    },
    // {
    //   title: '圈子ID',
    //   key: 'id',
    //   dataIndex: 'id',
    //   width: 90,
    // },
    {
      title: '圈子名称',
      dataIndex: 'name',
      render: (text: string, record: any) =>
        canSeeContent ? (
          <a onClick={() => history.push(`/view/circleContentMgr/${record.id}/${record.name}`)}>
            {text}
          </a>
        ) : (
          text
        ),
    },
    {
      title: '圈子头像',
      key: 'logo_url',
      dataIndex: 'logo_url',
      render: (text: string) => (
        <img
          src={text}
          className="list-pic"
          style={{
            height: '60px',
          }}
        />
      ),
      width: 90,
    },
    {
      title: '分类',
      dataIndex: 'circle_class_name',
      width: 90,
    },
    {
      title: (
        <div>
          内容数&nbsp;
          <Tooltip
            overlayStyle={{ maxWidth: 255 }}
            title="1、后台统计的内容数包括所有审核通过的内容，不管是否沉底；2、前台圈子下仅显示正常通过、推荐或圈外沉底的内容，不显示全局沉底内容"
            placement="top"
          >
            <Icon type="question-circle" />
          </Tooltip>
        </div>
      ),
      dataIndex: 'article_count',
      width: 90,
      render: (text: string, record: any) =>
        canSeeContent ? (
          <a onClick={() => history.push(`/view/circleContentMgr/${record.id}/${record.name}`)}>
            {text}
          </a>
        ) : (
          text
        ),
    },
    {
      title: '精选内容',
      dataIndex: 'selected_count',
      width: 90,
      render: (text: string, record: any) =>
        canSeeContent ? (
          <a
            onClick={() =>
              history.push(`/view/circleContentMgr/${record.id}/${record.name}?selected=1`)
            }
          >
            {text}
          </a>
        ) : (
          text
        ),
    },
    {
      title: '版块数',
      dataIndex: 'board_count',
      width: 80,
      render: (text: string, record: any) =>
        canSeeBlock ? (
          <a onClick={() => history.push(`/view/circleBlockMgr/${record.id}/${record.name}`)}>
            {text}
          </a>
        ) : (
          text
        ),
    },
    {
      title: '加入人数',
      dataIndex: 'join_count',
      width: 80,
    },
    {
      title: '状态',
      dataIndex: 'enabled',
      width: 90,
      render(text: boolean) {
        return text ? '已上线' : '已下线';
      },
    },
    {
      title: '创建人',
      dataIndex: 'created_by',
    },
    {
      title: '最后操作时间',
      dataIndex: 'updated_at',
      width: 105,
      render: (text: any) => (
        <div style={{ width: 80 }}>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</div>
      ),
    },
    {
      title: '操作',
      key: 'op',
      align: 'center',
      fixed: 'right',
      width: 90,
      render: (text: any, record: any, i: number) => getDropDown(record, i),
    },
  ];

  const changeFilter = (key: string, val: any, goToFirstPage = false) => {
    const newFilter = {
      ...filter,
      [key]: val,
    };
    setFilter(newFilter);
    getData(goToFirstPage, newFilter);
  };

  const handleKey = (e: { which: number }) => {
    if (e.which === 13) {
      const newFilter = {
        ...filter,
        ...searchState,
      };
      setFilter(newFilter);
      getData(true, newFilter);
    }
  };

  const getData = (goToFirstPage = false, newFilter = filter) => {
    let cur = goToFirstPage ? 1 : current;
    dispatch(
      getTableList('getCircleList', 'list', { current: cur, size: size || 10, ...newFilter })
    );
  };

  const submitEnd = (goToFirstPage: boolean) => {
    getData(goToFirstPage);
    setCircleDrawer({
      visible: false,
      record: null,
      key: Date.now(),
    });
  };

  const getCircleClassList = () => {
    api.getCircleClassList({}).then((res: any) => {
      setCircleClassList(res.data?.list || []);
    });
  };

  const editRecommendCircle = () => {
    dispatch(setConfig({ loading: true }));
    api
      .getCircleConfigDetail({})
      .then((res: any) => {
        dispatch(setConfig({ loading: false }));
        setSelectedCircle({
          visible: true,
          records: res.data || {},
          key: Date.now(),
        });
      })
      .catch(() => {
        dispatch(setConfig({ loading: false }));
      });
  };

  useEffect(() => {
    const { selectKeys, openKeys } = props;
    dispatch(setConfig({ selectKeys, openKeys }));
    getData(true);

    getCircleClassList();
  }, []);

  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          <PermButton perm="circle:create" onClick={() => handleEditCircle(null)}>
            创建圈子
          </PermButton>
          <PermButton
            perm="h5Content:view:12"
            style={{ marginLeft: 8 }}
            onClick={() => {
              history.push('/view/activeValueRule');
            }}
          >
            圈友活跃值规则
          </PermButton>
          <PermButton
            perm="circle_class:list"
            style={{ marginLeft: 8 }}
            onClick={() => {
              history.push('/view/circleCategoryMgr');
            }}
          >
            圈子分类
          </PermButton>

          <PermButton
            perm=""
            style={{ marginLeft: 8 }}
            onClick={() => {
              editRecommendCircle();
            }}
          >
            推荐圈子
          </PermButton>
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb(props.breadCrumb)}
        </Col>
      </Row>
      <div className="component-content">
        <Row style={{ marginBottom: 16 }}>
          <Col span={12}>
            <Select
              style={{ width: 100 }}
              value={filter.enabled}
              onChange={(val) => changeFilter('enabled', val)}
            >
              <Select.Option value="">圈子状态</Select.Option>
              <Select.Option value="true">已上线</Select.Option>
              <Select.Option value="false">已下线</Select.Option>
            </Select>
            {/* <Select
              style={{ width: 100, marginLeft: 8 }}
              value={filter.show_style}
              onChange={(val) => changeFilter('show_style', val)}
            >
              <Select.Option value="">展示样式</Select.Option>
              <Select.Option value={0}>单列</Select.Option>
              <Select.Option value={1}>双列</Select.Option>
            </Select> */}
            <Select
              style={{ width: 100, marginLeft: 8 }}
              value={`${filter.circle_class_id}`}
              onChange={(val) => changeFilter('circle_class_id', val)}
            >
              <Select.Option value="">圈子分类</Select.Option>
              {circleClassList.map((item) => (
                <Select.Option value={`${item.id}`}>{item.name}</Select.Option>
              ))}
            </Select>
          </Col>
          <Col span={12} style={{ textAlign: 'right' }}>
            <Select value={searchState.search_type} style={{ width: 100, marginRight: 8 }}>
              <Select.Option value={1}>圈子名称</Select.Option>
            </Select>
            <Input
              value={searchState.keyword}
              style={{ marginRight: 8, width: 180 }}
              onChange={(e: any) => setSearchState({ ...searchState, keyword: e.target.value })}
              onKeyPress={handleKey}
              placeholder="输入搜索内容"
            />
            <Button onClick={() => handleKey({ which: 13 })}>
              <Icon type="search" /> 搜索
            </Button>
          </Col>
        </Row>
        <Table
          func="getCircleList"
          index="list"
          filter={filter}
          pagination={true}
          rowKey="id"
          columns={columns}
          tableProps={{ scroll: { x: true } }}
        />
        <AddCircleDrawer
          key={circleDrawer.key}
          record={circleDrawer.record}
          visible={circleDrawer.visible}
          onClose={() => {
            setCircleDrawer({
              visible: false,
              record: null,
              key: Date.now(),
            });
          }}
          onEnd={submitEnd}
        />

        <AddSelectedCircleModal
          {...selectedCircle}
          onCancel={() => {
            setSelectedCircle({
              ...selectedCircle,
              visible: false,
            });
          }}
          onOk={() => {
            setSelectedCircle({
              ...selectedCircle,
              visible: false,
            });
          }}
        ></AddSelectedCircleModal>
      </div>
    </>
  );
}
