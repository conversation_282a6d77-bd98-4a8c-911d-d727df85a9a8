import NewNewsSearchAndInput from '@app/components/common/newNewsSearchAndInput';
import { Button, Form, Icon, InputNumber, Modal, Spin, Tooltip, message } from 'antd';
import React, { forwardRef, useImperativeHandle, useState } from 'react';

import { communityApi, opApi } from '@app/api';
import { Drawer, SearchAndInput } from '@app/components/common';
import { useDispatch, useStore } from 'react-redux';
import { max } from 'lodash';
import { setConfig } from '@app/action/config';

const AddSelectedCircleModal = (props: any, ref: any) => {
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);
  const { getFieldDecorator } = props.form;
  const store = useStore();
  const { permissions } = store.getState().session;

  const column = [
    {
      title: '序号',
      dataIndex: 'seq',
      width: 80,
      render: (text: any, record: any, i: number) => <span>{i + 1}</span>,
    },
    {
      title: '圈子名称',
      key: 'name',
      dataIndex: 'name',
    },
  ];

  const formLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 18 },
  };

  const handleSubmit = () => {
    props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        dispatch(setConfig({ mLoading: true }));
        const parmas = {
          ref_extensions: values.circle_list.map((v: any) => v.id).join(','),
        };
        communityApi
          .saveCircleConfig(parmas)
          .then((res: any) => {
            message.success('操作成功');
            dispatch(setConfig({ mLoading: false }));
            props.onOk && props.onOk();
          })
          .catch(() => {
            // message.error('添加失败');
            dispatch(setConfig({ mLoading: false }));
          });
      } else {
        message.error('请检查表单内容');
        dispatch(setConfig({ mLoading: false }));
      }
    });
  };

  return (
    <Drawer
      skey={props.key}
      visible={props.visible}
      onClose={props.onCancel}
      title={
        <>
          推荐圈子
          <span
            style={{
              color: '#999',
              fontSize: 12,
            }}
          >
            显示在V7.6及以后版本的潮圈首页-发现潮圈模块、新用户批量推荐加入圈子弹框等地方
          </span>
        </>
      }
      onOk={handleSubmit}
      maskClosable={false}
      okPerm="circle:config_save"
    >
      <Form {...formLayout} onSubmit={handleSubmit}>
        <Form.Item label="圈子" required>
          {getFieldDecorator('circle_list', {
            initialValue: props.records?.circle_list || [],
            rules: [
              {
                max: 50,
                message: '最多可添加50个推荐圈子',
                type: 'array',
              },
            ],
          })(
            <NewNewsSearchAndInput
              draggable={true}
              max={50}
              func="getCircleList"
              columns={column}
              placeholder="输入名字搜索"
              body={{ enabled: true }}
              categoryTip="圈子"
              order={true}
              addOnTop={false}
              searchKey="keyword"
              indexKey="list"
              apiWithPagination={true}
              selectMap={(record: any) => `${record.name}`}
            />
          )}
        </Form.Item>
      </Form>
    </Drawer>
  );
};

export default Form.create<any>({ name: 'AddSelectedCircleModal' })(
  forwardRef<any, any>(AddSelectedCircleModal)
);
